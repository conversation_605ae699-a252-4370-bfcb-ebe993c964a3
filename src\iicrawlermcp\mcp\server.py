"""
MCP Server implementation for iICrawlerMCP.

This module provides a FastMCP server that exposes CrawlerAgent functionality
through the Model Context Protocol.
"""

import logging
from typing import Optional

from mcp.server.fastmcp import FastMCP

logger = logging.getLogger(__name__)

# Create MCP server instance
mcp = FastMCP("iICrawlerMCP", description="Intelligent web crawler with multi-agent automation")

@mcp.tool()
def intelligent_web_task(task_description: str) -> str:
    """
    Execute intelligent web automation tasks using CrawlerAgent.
    
    This tool provides access to the full capabilities of iICrawlerMCP's CrawlerAgent,
    including web navigation, element interaction, screenshot analysis, and intelligent
    task delegation to specialized agents.
    
    Args:
        task_description: A natural language description of the web automation task
                         to perform. Examples:
                         - "Navigate to Google and search for Python tutorials"
                         - "Go to GitHub, find the trending repositories, and take a screenshot"
                         - "Visit a shopping site and find products under $50"
    
    Returns:
        A string containing the execution result, including any data extracted,
        actions performed, or error messages.
    """
    try:
        # Import here to avoid circular imports and ensure browser is available when needed
        from ..agents.crawler_agent import CrawlerAgent
        
        logger.info(f"MCP: Executing web task: {task_description}")
        
        # Create and use CrawlerAgent
        agent = CrawlerAgent()
        result = agent.invoke(task_description)
        
        # Clean up resources
        agent.cleanup()
        
        # Extract the output from the result
        output = result.get("output", str(result))
        
        logger.info("MCP: Web task completed successfully")
        return output
        
    except Exception as e:
        error_msg = f"Web task failed: {str(e)}"
        logger.error(f"MCP: {error_msg}")
        return error_msg

# Compatibility aliases for the main package
MCPServer = mcp
start_mcp_server = mcp.run

# Export the tool function for direct use
__all__ = ["mcp", "MCPServer", "start_mcp_server", "intelligent_web_task"]
