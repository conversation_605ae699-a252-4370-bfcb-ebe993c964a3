#!/usr/bin/env python3
"""
Integration test for iICrawlerMCP MCP functionality.

This script tests the MCP integration without requiring a full MCP client.
"""

import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_mcp_imports():
    """Test MCP module imports."""
    print("🧪 Testing MCP imports...")
    
    try:
        from iicrawlermcp import MCPServer, start_mcp_server, intelligent_web_task, _MCP_AVAILABLE
        print(f"✅ Main package MCP imports successful")
        print(f"   _MCP_AVAILABLE: {_MCP_AVAILABLE}")
        
        from iicrawlermcp.mcp import MCPServer as DirectMCPServer
        print(f"✅ Direct MCP module imports successful")
        
        return True
    except Exception as e:
        print(f"❌ MCP imports failed: {e}")
        return False

def test_crawler_agent():
    """Test CrawlerAgent functionality."""
    print("\n🧪 Testing CrawlerAgent...")
    
    try:
        from iicrawlermcp.agents import CrawlerAgent
        
        # Test agent creation
        agent = CrawlerAgent()
        print(f"✅ CrawlerAgent created with {len(agent.tools)} tools")
        
        # Test cleanup
        agent.cleanup()
        print("✅ CrawlerAgent cleanup successful")
        
        return True
    except Exception as e:
        print(f"❌ CrawlerAgent test failed: {e}")
        return False

def test_mcp_tool_function():
    """Test the MCP tool function directly."""
    print("\n🧪 Testing MCP tool function...")
    
    try:
        from iicrawlermcp.mcp.server import intelligent_web_task
        
        # Test with a simple task that should fail gracefully
        # (since we don't have a browser running)
        result = intelligent_web_task("test task - this should fail gracefully")
        print(f"✅ MCP tool function executed")
        print(f"   Result type: {type(result)}")
        print(f"   Result preview: {str(result)[:100]}...")
        
        return True
    except Exception as e:
        print(f"❌ MCP tool function test failed: {e}")
        return False

def test_mcp_server_creation():
    """Test MCP server creation."""
    print("\n🧪 Testing MCP server creation...")
    
    try:
        from iicrawlermcp.mcp.server import mcp
        
        print(f"✅ MCP server created: {mcp.name}")
        print(f"   Server type: {type(mcp)}")
        
        return True
    except Exception as e:
        print(f"❌ MCP server creation failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Starting iICrawlerMCP MCP Integration Tests\n")
    
    tests = [
        test_mcp_imports,
        test_crawler_agent,
        test_mcp_tool_function,
        test_mcp_server_creation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! MCP integration is working correctly.")
        return 0
    else:
        print("❌ Some tests failed. Please check the output above.")
        return 1

if __name__ == '__main__':
    sys.exit(main())
