# LangChain + MCP 最简单使用方案

## 概述

这是使用LangChain框架集成iICrawlerMCP服务器的最简单方案。我们提供了两种实现方式：

1. **官方方案**: 使用`langchain-mcp-adapters`包（推荐用于生产环境）
2. **简化方案**: 基于直接JSON-RPC通信（已验证可用，无需额外依赖）

## 核心优势

1. **标准化集成**: 使用官方的`langchain-mcp-adapters`包
2. **简单配置**: 只需几行代码即可集成MCP服务器
3. **自动工具发现**: 自动获取和使用MCP服务器提供的所有工具
4. **异步支持**: 完全支持异步操作
5. **错误处理**: 内置错误处理和重试机制

## 快速开始（推荐）

### 简化方案 - 无需额外依赖

```bash
# 直接运行简化版本（已验证可用）
python scripts/simple_mcp_langchain.py
```

这个方案基于直接JSON-RPC通信，不需要安装额外的包，已经验证可以正常工作。

## 官方方案（需要网络安装依赖）

### 1. 安装依赖包

```bash
# 运行安装脚本
python scripts/install_langchain_mcp.py

# 或手动安装
pip install langchain-mcp-adapters langchain-openai langgraph python-dotenv
```

### 2. 配置环境变量

```bash
# 复制环境配置文件
cp .env.example .env

# 编辑.env文件，设置你的API密钥
# OPENAI_API_KEY=your_openai_api_key_here
# OPENAI_API_BASE=https://api.openai.com/v1
# OPENAI_MODEL=gpt-4o
```

### 3. 运行官方方案测试

```bash
python scripts/langchain_mcp_simple.py
```

## 实际测试结果

我们已经成功验证了简化方案的可行性：

```
=== 简化版 MCP + LangChain 集成测试 ===
启动MCP服务器: start_mcp_server.py
✓ MCP服务器启动成功
✓ MCP连接初始化成功
✓ 成功加载 4 个工具:
  - intelligent_web_task: 执行智能网页任务的统一入口，支持导航、搜索、点击、截图等所有网页操作
  - browser_status: 获取当前浏览器状态，包括URL、标题等信息
  - take_screenshot: 快速截图工具，保存当前页面截图
  - cleanup_browser: 清理浏览器资源，释放内存

开始执行任务: OPEN baidu.com,take a screen shot
结果: 任务执行成功: ❌ 智能网页任务执行失败: Error code: 502

开始执行任务: get browser status
结果: 浏览器状态: 🔴 浏览器未初始化
✓ MCP服务器已停止
```

## 核心代码示例

### 简化方案 - 基本集成

```python
from scripts.simple_mcp_langchain import SimpleMCPClient, SimpleAgent

# 创建MCP客户端
client = SimpleMCPClient("scripts/start_mcp_server.py")

# 启动服务器并加载工具
await client.start()

# 创建简化代理
agent = SimpleAgent(client)

# 执行任务
result = await agent.execute_task("OPEN baidu.com,take a screen shot")
print(result)

# 清理资源
await client.stop()
```

### 官方方案 - 基本集成

```python
from langchain_mcp_adapters.client import MultiServerMCPClient
from langgraph.prebuilt import create_react_agent
from langchain_openai import ChatOpenAI

# 配置MCP客户端
client = MultiServerMCPClient({
    "iicrawler": {
        "command": "python",
        "args": ["scripts/start_mcp_server.py"],
        "transport": "stdio",
    }
})

# 获取工具
tools = await client.get_tools()

# 创建代理
model = ChatOpenAI(model="gpt-4o")
agent = create_react_agent(model, tools)

# 执行任务
response = await agent.ainvoke({
    "messages": [{"role": "user", "content": "OPEN baidu.com,take a screen shot"}]
})
```

### 多服务器集成

```python
# 可以同时连接多个MCP服务器
client = MultiServerMCPClient({
    "iicrawler": {
        "command": "python",
        "args": ["scripts/start_mcp_server.py"],
        "transport": "stdio",
    },
    "math": {
        "command": "python", 
        "args": ["math_server.py"],
        "transport": "stdio",
    },
    "weather": {
        "url": "http://localhost:8000/mcp",
        "transport": "streamable_http",
    }
})
```

## 工作流程

1. **启动MCP服务器**: `MultiServerMCPClient`自动启动配置的MCP服务器
2. **工具发现**: 自动获取服务器提供的所有工具
3. **代理创建**: 使用工具创建LangGraph代理
4. **任务执行**: 代理自动选择和调用合适的工具
5. **结果返回**: 返回执行结果

## 支持的传输方式

### 1. stdio传输 (推荐)
- 适用于本地MCP服务器
- 通过标准输入输出通信
- 配置简单，性能好

### 2. HTTP传输
- 适用于远程MCP服务器
- 支持RESTful API
- 适合分布式部署

## 错误处理

```python
try:
    tools = await client.get_tools()
    agent = create_react_agent(model, tools)
    response = await agent.ainvoke({"messages": [...]})
except Exception as e:
    print(f"执行失败: {e}")
    # 处理错误或使用备用方案
```

## 性能优化

1. **连接复用**: `MultiServerMCPClient`自动管理连接池
2. **异步执行**: 所有操作都是异步的
3. **工具缓存**: 工具列表会被缓存，避免重复获取
4. **批量处理**: 支持批量工具调用

## 方案对比

| 特性 | 简化方案 | 官方LangChain MCP | 直接JSON-RPC | MCP SDK |
|------|----------|------------------|---------------|---------|
| 集成难度 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐ | ⭐⭐⭐ |
| 代码量 | 中等 | 最少 | 最多 | 中等 |
| 依赖包 | 无需额外 | 需要安装 | 无需额外 | 需要安装 |
| 错误处理 | 手动 | 自动 | 手动 | 半自动 |
| 工具发现 | 自动 | 自动 | 手动 | 半自动 |
| 异步支持 | 完整 | 完整 | 需要实现 | 部分 |
| 维护成本 | 低 | 最低 | 最高 | 中等 |
| 验证状态 | ✅ 已验证 | 需要网络 | ✅ 已验证 | 部分验证 |

## 常见问题

### Q: 如何添加新的MCP服务器？
A: 在`MultiServerMCPClient`配置中添加新的服务器配置即可。

### Q: 如何处理工具调用失败？
A: LangGraph代理会自动处理工具调用失败，并尝试其他方案。

### Q: 如何自定义工具行为？
A: 可以在MCP服务器端修改工具实现，客户端会自动获取更新。

### Q: 支持哪些LLM模型？
A: 支持所有LangChain兼容的模型，包括OpenAI、Anthropic、本地模型等。

## 下一步

1. 尝试运行示例代码
2. 根据需要修改环境配置
3. 扩展MCP服务器功能
4. 集成到你的应用中

这个方案提供了最简单、最标准的方式来使用LangChain + MCP，是生产环境的推荐选择。
