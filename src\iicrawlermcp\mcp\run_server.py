#!/usr/bin/env python3
"""
MCP Server runner for iICrawlerMCP.

This script provides a command-line interface to start the MCP server
with different transport options.
"""

import sys
import os
import argparse
import logging

# Add src to path for development
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from iicrawlermcp.mcp.server import mcp

def setup_logging(verbose: bool = False):
    """Setup logging configuration."""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def main():
    """Main entry point for the MCP server."""
    parser = argparse.ArgumentParser(description='iICrawlerMCP MCP Server')
    parser.add_argument(
        'transport',
        choices=['stdio', 'sse'],
        help='Transport method to use'
    )
    parser.add_argument(
        '--port',
        type=int,
        default=8000,
        help='Port for SSE transport (default: 8000)'
    )
    parser.add_argument(
        '--host',
        default='localhost',
        help='Host for SSE transport (default: localhost)'
    )
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose logging'
    )
    
    args = parser.parse_args()
    
    setup_logging(args.verbose)
    
    if args.transport == 'stdio':
        print("Starting iICrawlerMCP MCP Server with stdio transport...", file=sys.stderr)
        mcp.run()
    elif args.transport == 'sse':
        print(f"Starting iICrawlerMCP MCP Server with SSE transport...", file=sys.stderr)
        print(f"Note: FastMCP will use default host and port settings", file=sys.stderr)
        mcp.run(transport='sse')

if __name__ == '__main__':
    main()
