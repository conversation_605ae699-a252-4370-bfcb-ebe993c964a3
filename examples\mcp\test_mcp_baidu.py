#!/usr/bin/env python3
"""
Test MCP functionality with a real web task: visit Baidu and take screenshot.

This script tests the intelligent_web_task function with a practical example.
"""

import sys
import os
import time

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_baidu_screenshot():
    """Test visiting Baidu and taking a screenshot."""
    print("🚀 Testing MCP with Baidu screenshot task...")
    
    try:
        from iicrawlermcp.mcp.server import intelligent_web_task
        
        # Define the task
        task = "访问百度首页 https://www.baidu.com 并截图"
        
        print(f"📋 Task: {task}")
        print("⏳ Executing task...")
        
        start_time = time.time()
        
        # Execute the task
        result = intelligent_web_task(task)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        print(f"✅ Task completed in {execution_time:.2f} seconds")
        print(f"📄 Result:")
        print("-" * 50)
        print(result)
        print("-" * 50)
        
        # Check if result indicates success
        if "screenshot" in result.lower() or "截图" in result.lower():
            print("🎉 Screenshot task appears successful!")
        elif "error" in result.lower() or "failed" in result.lower() or "失败" in result.lower():
            print("⚠️ Task completed but may have encountered issues")
        else:
            print("ℹ️ Task completed - check result details above")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_simple_navigation():
    """Test simple navigation without screenshot."""
    print("\n🚀 Testing simple navigation...")
    
    try:
        from iicrawlermcp.mcp.server import intelligent_web_task
        
        # Define a simpler task
        task = "访问百度首页 https://www.baidu.com,搜索少林寺,并获取页面标题"
        
        print(f"📋 Task: {task}")
        print("⏳ Executing task...")
        
        start_time = time.time()
        
        # Execute the task
        result = intelligent_web_task(task)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        print(f"✅ Task completed in {execution_time:.2f} seconds")
        print(f"📄 Result:")
        print("-" * 50)
        print(result)
        print("-" * 50)
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the tests."""
    print("🧪 Starting MCP Baidu Test Suite\n")
    
    tests = [
        ("Simple Navigation", test_simple_navigation),
        ("Baidu Screenshot", test_baidu_screenshot)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"Running: {test_name}")
        print('='*60)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name} PASSED")
        else:
            print(f"❌ {test_name} FAILED")
    
    print(f"\n📊 Final Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! MCP is working correctly with real web tasks.")
        return 0
    else:
        print("⚠️ Some tests failed. Check the output above for details.")
        return 1

if __name__ == '__main__':
    sys.exit(main())
