"""
HTTP API server for iICrawlerMCP.

Provides REST API endpoints for web automation tasks.
"""

import logging
import uvicorn
from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional

from ..mcp.server import intelligent_web_task

logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="iICrawlerMCP API",
    description="Intelligent web crawler with multi-agent automation",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure this for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Request/Response models
class WebTaskRequest(BaseModel):
    task_description: str
    timeout: Optional[int] = 60

class WebTaskResponse(BaseModel):
    success: bool
    result: str
    execution_time: Optional[float] = None
    error: Optional[str] = None

@app.get("/")
async def root():
    """Root endpoint with API information."""
    return {
        "name": "iICrawlerMCP API",
        "version": "1.0.0",
        "description": "Intelligent web crawler with multi-agent automation",
        "endpoints": {
            "/execute": "POST - Execute web automation task",
            "/health": "GET - Health check",
            "/docs": "GET - API documentation"
        }
    }

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "service": "iICrawlerMCP"}

@app.post("/execute", response_model=WebTaskResponse)
async def execute_web_task(request: WebTaskRequest):
    """
    Execute a web automation task.
    
    Args:
        request: WebTaskRequest containing task description
        
    Returns:
        WebTaskResponse with execution result
    """
    try:
        import time
        start_time = time.time()
        
        logger.info(f"API: Executing web task: {request.task_description}")
        
        # Execute the task using MCP function
        result = intelligent_web_task(request.task_description)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # Check if result indicates an error
        is_error = any(keyword in result.lower() for keyword in ['failed', 'error', '失败', '错误'])
        
        response = WebTaskResponse(
            success=not is_error,
            result=result,
            execution_time=execution_time,
            error=result if is_error else None
        )
        
        logger.info(f"API: Task completed in {execution_time:.2f}s, success: {response.success}")
        return response
        
    except Exception as e:
        error_msg = f"Task execution failed: {str(e)}"
        logger.error(f"API: {error_msg}")
        
        return WebTaskResponse(
            success=False,
            result="",
            error=error_msg
        )

def start_api_server(host: str = "0.0.0.0", port: int = 8080, reload: bool = False):
    """
    Start the API server.
    
    Args:
        host: Host to bind to
        port: Port to bind to
        reload: Enable auto-reload for development
    """
    uvicorn.run(
        "iicrawlermcp.api.server:app",
        host=host,
        port=port,
        reload=reload,
        log_level="info"
    )
